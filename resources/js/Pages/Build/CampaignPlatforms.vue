<template>
    <BuildLayout title="Choose Social Platforms" :auth="auth" :currentStep="1">
        <div class="container">
            <section
                class="overflow-hidden rounded-[20px] border border-border-primary shadow-md"
            >
                <header
                    class="flex justify-between bg-surface-neutral-lightest-grey p-20"
                >
                    <button
                        @click.prevent="handleBack"
                        class="flex items-center gap-4 py-8 font-header text-lg font-bold text-text-body"
                    >
                        <IconArrowLeftBold />
                        Back to audience results
                    </button>

                    <button
                        @click.prevent="continueToContent"
                        class="flex items-center gap-4 py-8 font-header text-lg font-bold text-text-body"
                        :disabled="!hasSelectedPlatforms || isSubmitting"
                    >
                        Create content
                        <IconArrowRightBold class="stroke-text-body" />
                    </button>
                </header>

                <div class="p-24">
                    <!-- Instructions -->
                    <div class="mb-32 space-y-20">
                        <h1 class="mb-8 font-header text-3xl font-bold text-text-body text-center">
                            Choose Social Platforms
                        </h1>
                        <p class="text-lg text-text-body text-center">
                            Based on your audience analysis, select platforms to create content for your campaign.
                        </p>
                        <div v-if="recommendedPlatforms.length > 0" class="text-center">
                            <p class="text-sm text-text-secondary">
                                <strong>Recommended platforms:</strong> {{ recommendedPlatforms.join(', ') }}
                            </p>
                        </div>
                    </div>

                    <!-- Platform Selection -->
                    <div class="space-y-8 mb-28">
                        <div v-for="platform in platforms" :key="platform.id"
                            class="flex items-center justify-between rounded-lg border p-12 hover:border-border-action transition-all cursor-pointer"
                            :class="{
                                'border-border-action bg-surface-action-2x-light': selectedPlatforms[platform.id],
                                'border-border-primary': !selectedPlatforms[platform.id]
                            }"
                            @click="togglePlatform(platform.id)">
                            <div class="flex items-center space-x-12">
                                <input type="checkbox" :id="platform.id" v-model="selectedPlatforms[platform.id]"
                                    class="size-18 rounded border-border-primary text-surface-action focus:ring-surface-action pointer-events-none" />
                                <div class="flex items-center space-x-8">
                                    <div class="flex size-32 items-center justify-center rounded-full"
                                        :style="{ backgroundColor: platform.color + '20' }">
                                        <VsxIcon :iconName="platform.iconName" :size="20" :color="platform.color"
                                            type="linear" />
                                    </div>
                                    <div class="flex flex-col">
                                        <span class="font-medium text-text-body">{{ platform.name }}</span>
                                        <span v-if="isRecommended(platform.id)" class="text-xs text-surface-action font-medium">
                                            Recommended for your audience
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center space-x-10">
                                <span class="text-md text-text-secondary min-w-[60px] text-right mr-8">
                                    {{ postCounts[platform.id] || 1 }} post(s)
                                </span>
                                <div class="flex items-center space-x-6">
                                    <Button @click.stop="decrementCount(platform.id)"
                                        :disabled="!selectedPlatforms[platform.id] || postCounts[platform.id] <= 1">
                                        <VsxIcon iconName="Minus" :size="16" type="linear" />
                                    </Button>
                                    <Button @click.stop="incrementCount(platform.id)"
                                        :disabled="!selectedPlatforms[platform.id] || postCounts[platform.id] >= 5">
                                        <VsxIcon iconName="Add" :size="16" type="linear" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end items-center">
                        <Button size="lg" class="space-x-8" @click="continueToContent"
                            :disabled="!hasSelectedPlatforms || isSubmitting" color="action">
                            <span>{{ isSubmitting ? 'Processing...' : 'Generate Content' }}</span>
                            <VsxIcon iconName="ArrowRight" :size="20" type="linear" />
                        </Button>
                    </div>
                </div>
            </section>
        </div>
    </BuildLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { router } from "@inertiajs/vue3"
import BuildLayout from '@/Layouts/BuildLayout.vue'
import Button from '@/Components/Button/Button.vue'
import { VsxIcon } from 'vue-iconsax'
import IconArrowLeftBold from "@/Components/Icons/IconArrowLeftBold.vue"
import IconArrowRightBold from "@/Components/Icons/IconArrowRightBold.vue"
import { useFindDonorsStore } from "@/stores/findDonors"

const props = defineProps({
    auth: Object,
    social_media_channels: Object,
    audienceData: {
        type: Object,
        default: () => ({})
    }
})

const findDonorsStore = useFindDonorsStore()

// Platform options with colors matching the design
const platforms = ref([
    { id: 'twitter', name: 'Twitter', iconName: 'Hashtag', color: '#1DA1F2' },
    { id: 'instagram', name: 'Instagram', iconName: 'Instagram', color: '#E4405F' },
    { id: 'linkedin', name: 'LinkedIn', iconName: 'UserSquare', color: '#0077B5' },
    { id: 'facebook', name: 'Facebook', iconName: 'Facebook', color: '#1877F2' },
    { id: 'pinterest', name: 'Pinterest', iconName: 'Heart', color: '#BD081C' },
    { id: 'tiktok', name: 'TikTok', iconName: 'VideoPlay', color: '#000000' },
    { id: 'threads', name: 'Threads', iconName: 'MessageText', color: '#000000' },
    { id: 'bluesky', name: 'Bluesky', iconName: 'Cloud', color: '#00A8E8' },
    { id: 'youtube', name: 'YouTube', iconName: 'Youtube', color: '#FF0000' },
    { id: 'blog', name: 'Blog', iconName: 'DocumentText1', color: '#6B7280' },
])

// Form data
const selectedPlatforms = ref({})
const postCounts = ref({})
const isSubmitting = ref(false)

// Initialize post counts for all platforms
platforms.value.forEach(platform => {
    postCounts.value[platform.id] = 1
})

// Get recommended platforms from audience data
const recommendedPlatforms = computed(() => {
    const firstDonor = findDonorsStore.donorsResults?.[0]

    if (!firstDonor?.AGE || !firstDonor?.GENDER || !props.social_media_channels) {
        return []
    }

    const ageRange = firstDonor.AGE
    const gender = firstDonor.GENDER.toLowerCase()
    const channelsForDemographic = props.social_media_channels[ageRange]?.[gender] || []
    return channelsForDemographic.map(channel => channel.toLowerCase())
})

// Computed
const hasSelectedPlatforms = computed(() => {
    return Object.values(selectedPlatforms.value).some(selected => selected)
})

// Methods
const togglePlatform = (platformId) => {
    selectedPlatforms.value[platformId] = !selectedPlatforms.value[platformId]
}

const incrementCount = (platformId) => {
    if (postCounts.value[platformId] < 5) {
        postCounts.value[platformId]++
    }
}

const decrementCount = (platformId) => {
    if (postCounts.value[platformId] > 1) {
        postCounts.value[platformId]--
    }
}

const isRecommended = (platformId) => {
    return recommendedPlatforms.value.includes(platformId)
}

const handleBack = () => {
    router.get("/build/audience-result")
}

const continueToContent = async () => {
    if (!hasSelectedPlatforms.value) return

    isSubmitting.value = true

    try {
        const selectedData = {}
        Object.keys(selectedPlatforms.value).forEach(platformId => {
            if (selectedPlatforms.value[platformId]) {
                selectedData[platformId] = postCounts.value[platformId]
            }
        })

        console.log('Selected platforms:', selectedData)

        // Submit platform selection and redirect to content creation
        router.post('/build/campaign/platforms', {
            platforms: selectedData,
            audienceData: props.audienceData
        }, {
            onSuccess: () => {
                console.log('Campaign platform selection saved successfully')
            },
            onError: (errors) => {
                console.error('Error saving campaign platform selection:', errors)
            }
        })

    } catch (error) {
        console.error('Error:', error)
    } finally {
        isSubmitting.value = false
    }
}

// Pre-select recommended platforms on mount
onMounted(() => {
    recommendedPlatforms.value.forEach(platformId => {
        selectedPlatforms.value[platformId] = true
    })
})
</script>
